@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Poppins:wght@400;500;600&display=swap');

* {
    font-family: "Poppins", serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;

}

:root {
    /* Dark theme colors */
    --text-color: #edf3ff;
    --subheading-color: #97a7ca;
    --placeholder-color: #c3cdde;
    --primary-color: #101623;
    --secondary-color: #283045;
    --secondary-hover-color: #333e58;
    --scrollbar-color: #626a7f;
}

body {
    color: var(--text-color);
    background: var(--primary-color);
}

.chat-container {
    padding: 32px 0 60px;
    overflow-y: auto;
    max-height: calc(100vh - 127px);
    scrollbar-color: var(--scrollbar-color);
}

.chat-container :where(.app-header, .suggestions, .prompt-wrapper, .message, .disclaimer-text) {
    margin: 0 auto;
    width: 100%;
    padding: 0 20px;
    max-width: 980px;
}

/* App header styling */
.chat-container .app-header {
    margin-top: 4vh;
}

.app-header .heading {
    font-size: 3rem;
    width: fit-content;
    background: linear-gradient(to right, #1d7efd, #8f6fff); /* Change color of the heading*/
    background-clip: text; /* Makes the text background only for letters */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent; /* Makes the heading text transparent with color */
}

.app-header .subheading {
    font-size: 2.6rem;
    margin-top: -5px;
    color: var(--subheading-color)
}

/* Suggestions list styling */

.chat-container .suggestions {
    display: flex;
    gap: 15px;
    margin-top: 9.5vh;
    list-style: none;
    overflow-x: auto;
    scrollbar-width: none;
}

.chat-container :is(.app-header, .suggestions) {
    display: none;
}
.suggestions .suggestions-item {
    width: 228px;
    flex-shrink: 0;
    padding: 18px;
    background: var(--secondary-color);
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    cursor: pointer;
    border-radius: 12px; /* Makes border round */
    transition: 0.3s ease;
}
.suggestions .suggestions-item:hover {
    background: var(--secondary-hover-color);
}
.suggestions .suggestions-item .text {
    font-size: 1.1rem;
}

.suggestions .suggestions-item span {
    height: 45px;
    width: 45px;
    margin-top: 35px;
    display: flex;
    align-self: flex-end;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #1d7efd;
    background: var(--primary-color);
}
/* Colors the icons */
.suggestions .suggestions-item:nth-child(2) span {
    color: #28a745
}
.suggestions .suggestions-item:nth-child(3) span {
    color: #ffc107;
}
.suggestions .suggestions-item:nth-child(4) span {
    color: #6f42c1;
}

/* Chats container stylings */
.chat-container .chats-container {
    display: flex;
    gap: 20px;
    flex-direction: column;
}
.chats-container .message {
    display: flex;
    gap: 11px;
    align-items: center;
}
.chat-container .bot-message .avater {
    height: 43px;
    width: 43px;
    flex-shrink: 0;
    padding: 6px;
    align-self: flex-start;
    margin-right: -7px;
    border-radius: 50px;
    background: var(--secondary-color);
    border: 1px solid var(--secondary-hover-color);
}
.chat-container .bot-message .loading .avater {
    animation: rotate 3s linear infinite;
}
@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}
.chat-container .message .message-text {
    padding: 3px 16px;
    word-wrap: break-word;
    white-space: pre-line;
}
.chat-container .bot-message {
    margin: 9px auto;
}
.chat-container .user-message {
    flex-direction: column;
    align-items: flex-end;
}
/* The background of each chat of user and bot */
.chat-container .user-message .message-text {
    padding: 12px 16px;
    max-width: 75%;
    border-radius: 13px 13px 3px 13px;
    background: var(--secondary-color);
}

/* Prompt container styling */

.prompt-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 16px 0;
    background: var(--primary-color);
}
.prompt-container :where(.prompt-wrapper, .prompt-form, .prompt-actions) {
    display: flex;
    gap: 12px;
    height: 56px;
    align-items: center;
}
.prompt-wrapper .prompt-form {
    width: 100%;
    height: 100%;
    border-radius: 130px;
    background: var(--secondary-color);
}
.prompt-form .prompt-input {
    height: 100%;
    width: 100%;
    background: none;
    outline: none;
    border: none;
    font-size: 1rem;
    padding-left: 24px;
    color: var(--text-color);
}
.prompt-form .prompt-input::placeholder {
    color: var(--placeholder-color);
}
.prompt-wrapper button {
    width: 56px;
    height: 100%;
    border: none;
    cursor: pointer;
    border-radius: 50%;
    font-size: 1.4rem;
    flex-shrink: 0;
    color: var(--text-color);
    background-color: var(--secondary-color);
    transition: 0.3s ease;
}


.prompt-wrapper :is(button:hover, #file-icon, #cancle-file-btn ) {
    background: var(--secondary-hover-color);
}

.prompt-form #send-prompt-btn {
    color: #fff;
    display: none;
    background: #1d7efd;
}
.prompt-form .prompt-input:valid ~ .prompt-actions
#send-prompt-btn {
    display: block;
}
.prompt-form #send-prompt-btn:hover {
    background: #0264e3;
}
.prompt-wrapper .prompt-form :where(.file-upload-wrapper, button, img) {
    height: 45px;
    position: relative;
    width: 45px;
}
.prompt-form .prompt-actions {
    gap: 5px;
    margin-right: 7px;
}

.prompt-form .file-upload-wrapper :where(button, img) {
    position: absolute;
    border-radius: 50%;
    object-fit: cover;
/*    display: none; */
}
.prompt-form .file-upload-wrapper.active #add-file-btn,
.prompt-form .file-upload-wrapper.active.active.img-attachd img,
.prompt-form .file-upload-wrapper.active.active.file-attachd .file-icon,
.prompt-form .file-upload-wrapper.active:hover 
#cancle-file-btn { 
    display: block;
}
.prompt-form .file-upload-wrapper.active #add-file-btn {
    display: none;
}
.prompt-form #cancle-file-btn {
    color: #d62939;
}
.prompt-form #cancle-file-btn {
    color: #1d7efd;
}
.prompt-container .disclaimer-text {
    text-align: center;
    font-size: 0.9rem;
    padding: 16px 20px 0;
    color: var(--placeholder-color);
}