<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FarmGenius - Documentation</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
{% include 'navbar.html' %}
<div class="container mt-4">
    <h2>Documentation</h2>
    {% if current_user.is_authenticated and current_user.is_doc_poster %}
    <form method="POST">
        <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" required>
        </div>
        <div class="mb-3">
            <label for="content" class="form-label">Content</label>
            <textarea class="form-control" id="content" name="content" rows="4" required></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Post Documentation</button>
    </form>
    {% endif %}
    <hr>
    <h3>All Documentation</h3>
    {% for doc in docs %}
        <div class="card mb-3">
            <div class="card-body">
                <h5 class="card-title">{{ doc.title }}</h5>
                <p class="card-text">{{ doc.content }}</p>
                <hr>
                <p class="card-text"><small class="text-muted">Posted by <a href="/user/{{ doc.author.id }}">{{ doc.author.username }}</a></small></p>
            </div>
        </div>
    {% endfor %}
</div>
</body>
</html>