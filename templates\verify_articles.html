<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgriGenius - Verify Articles</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
{% include 'navbar.html' %}
<div class="container mt-4">
    <h2>Admin Panel - Verify Articles</h2>
    <p class="text-muted">Review and approve pending articles before they appear publicly.</p>
    
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                {% for message in messages %}
                    {{ message }}
                {% endfor %}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endif %}
    {% endwith %}
    
    {% if articles %}
        <div class="row">
            {% for article in articles %}
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Article #{{ article.id }}</h6>
                            <span class="badge bg-warning">Pending Review</span>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ article.title }}</h5>
                            <p class="card-text">{{ article.content[:200] }}{% if article.content|length > 200 %}...{% endif %}</p>
                            <hr>
                            <p class="card-text">
                                <small class="text-muted">
                                    Submitted by: <strong>{{ article.author.username }}</strong>
                                </small>
                            </p>
                        </div>
                        <div class="card-footer d-flex justify-content-between">
                            <a href="{{ url_for('approve_article', article_id=article.id) }}" 
                               class="btn btn-success btn-sm"
                               onclick="return confirm('Are you sure you want to approve this article?')">
                                <i class="fas fa-check"></i> Approve
                            </a>
                            <button class="btn btn-danger btn-sm" 
                                    onclick="rejectArticle({{ article.id }})"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#rejectModal{{ article.id }}">
                                <i class="fas fa-times"></i> Reject
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Reject Modal for each article -->
                <div class="modal fade" id="rejectModal{{ article.id }}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Reject Article</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>Are you sure you want to reject the article "<strong>{{ article.title }}</strong>"?</p>
                                <p class="text-muted">This action cannot be undone. The article will be permanently deleted.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <a href="{{ url_for('reject_article', article_id=article.id) }}" 
                                   class="btn btn-danger">Confirm Reject</a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info" role="alert">
            <h4 class="alert-heading">No Pending Articles</h4>
            <p>There are currently no articles waiting for approval.</p>
            <hr>
            <p class="mb-0">
                <a href="{{ url_for('articles') }}" class="btn btn-primary">View All Articles</a>
                <a href="{{ url_for('profile') }}" class="btn btn-secondary">Back to Profile</a>
            </p>
        </div>
    {% endif %}
    
    <div class="mt-4">
        <a href="{{ url_for('profile') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Profile
        </a>
        <a href="{{ url_for('verify_docs') }}" class="btn btn-info">
            <i class="fas fa-file-alt"></i> Verify Documentation
        </a>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>
