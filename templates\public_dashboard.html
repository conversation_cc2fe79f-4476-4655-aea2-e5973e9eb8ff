<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Public Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
{% include 'navbar.html' %}
<div class="container mt-5">
    <h2 class="mb-4">Active Users</h2>
    <div class="row">
        {% for user in users %}
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <img src="https://ui-avatars.com/api/?name={{ user.username|capitalize }}&background=388e3c&color=fff&size=96" alt="Profile Picture" class="rounded-circle mb-3" style="width: 80px; height: 80px; object-fit: cover;">
                    <h5 class="card-title mb-1">{{ user.username|capitalize }}</h5>
                    <span class="badge bg-success">{{ 'Admin' if user.is_admin else 'Member' }}</span>
                    {% if user.is_doc_poster %}
                        <span class="badge bg-info text-dark">Documentation Poster</span>
                    {% endif %}
                    <hr>
                    <a href="/user/{{ user.id }}" class="btn btn-outline-primary btn-sm">View Profile</a>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12"><em>No active users yet.</em></div>
        {% endfor %}
    </div>
</div>
</body>
</html>
