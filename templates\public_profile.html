<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FarmGenius - User Profile</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
{% include 'navbar.html' %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <img src="https://ui-avatars.com/api/?name={{ user.username|capitalize }}&background=388e3c&color=fff&size=128" alt="Profile Picture" class="rounded-circle me-4" style="width: 100px; height: 100px; object-fit: cover;">
                        <div>
                            <h2 class="mb-0">{{ user.username|capitalize }}</h2>
                            <span class="badge bg-success">{{ 'Admin' if user.is_admin else 'Member' }}</span>
                            {% if user.is_doc_poster %}
                                <span class="badge bg-info text-dark">Documentation Poster</span>
                            {% endif %}
                        </div>
                    </div>
                    <hr>
                    <div class="mb-4">
                        <h4 class="mb-2">Achievements</h4>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">Articles posted: <strong>{{ articles|length }}</strong></li>
                            <li class="list-group-item">Documentation posted: <strong>{{ docs|length }}</strong></li>
                            <li class="list-group-item">Member since: <strong>--</strong> <!-- Placeholder, add join date if available --></li>
                        </ul>
                    </div>
                    <div class="mb-4">
                        <h4 class="mb-2">Articles by {{ user.username|capitalize }}</h4>
                        <div class="row">
                        {% for article in articles %}
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ article.title }}</h5>
                                        <p class="card-text">{{ article.content[:100] }}{% if article.content|length > 100 %}...{% endif %}</p>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="col-12"><em>No articles posted yet.</em></div>
                        {% endfor %}
                        </div>
                    </div>
                    <div>
                        <h4 class="mb-2">Documentation by {{ user.username|capitalize }}</h4>
                        <div class="row">
                        {% for doc in docs %}
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ doc.title }}</h5>
                                        <p class="card-text">{{ doc.content[:100] }}{% if doc.content|length > 100 %}...{% endif %}</p>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="col-12"><em>No documentation posted yet.</em></div>
                        {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
