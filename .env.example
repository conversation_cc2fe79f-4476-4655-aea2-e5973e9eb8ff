# Flask Configuration
# Generate a secure SECRET_KEY using: python -c "import secrets; print(secrets.token_hex(32))"
SECRET_KEY=your-super-secret-random-key-here

# Database Configuration
# For development, SQLite is fine. For production, use PostgreSQL or MySQL
DATABASE_URL=sqlite:///farmgenius.db

# Flask Environment
FLASK_ENV=development
FLASK_DEBUG=True

# Optional: n8n Chatbot URL (when implemented)
# N8N_CHATBOT_URL=https://your-n8n-instance.com/webhook/your-webhook-id

