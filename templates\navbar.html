<nav class="navbar navbar-expand-lg">
  <div class="container">
    <a class="navbar-brand" href="/">
      <i class="fas fa-seedling"></i>
      AgriGenius
    </a>

    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent"
            aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse" id="navbarContent">
      <ul class="navbar-nav me-auto">
        <li class="nav-item">
          <a class="nav-link" href="/">
            <i class="fas fa-home"></i> Home
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/articles">
            <i class="fas fa-newspaper"></i> Articles
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/documentation">
            <i class="fas fa-book"></i> Documentation
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/chat">
            <i class="fas fa-robot"></i> AI Assistant
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/marketplace">
            <i class="fas fa-store"></i> Marketplace
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/dashboard">
            <i class="fas fa-chart-line"></i> Dashboard
          </a>
        </li>
      </ul>

      <ul class="navbar-nav">
        <!-- Weather Widget -->
        <li class="nav-item me-3">
          <div class="weather-widget d-none d-lg-block">
            <span class="weather-temp">22°C</span>
            <i class="fas fa-sun weather-icon"></i>
          </div>
        </li>

        <!-- Notifications -->
        {% if current_user.is_authenticated %}
        <li class="nav-item dropdown me-2">
          <a class="nav-link position-relative" href="#" id="notificationDropdown" role="button"
             data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-bell"></i>
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
              3
            </span>
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown">
            <li><h6 class="dropdown-header">Notifications</h6></li>
            <li><a class="dropdown-item" href="#">
              <i class="fas fa-check-circle text-success"></i>
              Article approved
            </a></li>
            <li><a class="dropdown-item" href="#">
              <i class="fas fa-comment text-info"></i>
              New comment on your post
            </a></li>
            <li><a class="dropdown-item" href="#">
              <i class="fas fa-seedling text-warning"></i>
              Sensor alert: Low soil moisture
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-center" href="/notifications">View all</a></li>
          </ul>
        </li>
        {% endif %}

        <!-- Theme toggle -->
        <li class="nav-item me-2">
          <button class="btn theme-toggle" id="themeToggle" type="button" title="Toggle theme">
            <i class="fas fa-moon"></i>
          </button>
        </li>

        {% if current_user.is_authenticated %}
        <!-- User menu -->
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown"
             role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <img src="https://ui-avatars.com/api/?name={{ current_user.username }}&background=2e7d32&color=fff&size=32"
                 alt="Avatar" class="user-avatar me-2">
            <span class="d-none d-md-inline">{{ current_user.username }}</span>
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
            <li><a class="dropdown-item" href="/profile">
              <i class="fas fa-user"></i> Profile
            </a></li>
            <li><a class="dropdown-item" href="/my-posts">
              <i class="fas fa-edit"></i> My Posts
            </a></li>
            <li><a class="dropdown-item" href="/user/{{ current_user.id }}">
              <i class="fas fa-id-card"></i> Public Profile
            </a></li>
            {% if current_user.is_admin %}
            <li><hr class="dropdown-divider"></li>
            <li><h6 class="dropdown-header">Admin</h6></li>
            <li><a class="dropdown-item" href="/admin/verify_articles">
              <i class="fas fa-check-double"></i> Verify Articles
            </a></li>
            <li><a class="dropdown-item" href="/admin/verify_docs">
              <i class="fas fa-file-check"></i> Verify Docs
            </a></li>
            {% endif %}
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="/logout">
              <i class="fas fa-sign-out-alt"></i> Sign Out
            </a></li>
          </ul>
        </li>
        {% else %}
        <!-- Guest menu -->
        <li class="nav-item">
          <a class="btn btn-outline-primary me-2" href="/login">
            <i class="fas fa-sign-in-alt"></i> Login
          </a>
        </li>
        <li class="nav-item">
          <a class="btn btn-primary" href="/signup">
            <i class="fas fa-user-plus"></i> Sign Up
          </a>
        </li>
        {% endif %}
      </ul>
    </div>
  </div>
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Theme toggle functionality
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = themeToggle.querySelector('i');
  const savedTheme = localStorage.getItem('theme') || 'light';

  // Apply saved theme
  document.body.setAttribute('data-theme', savedTheme);
  updateThemeIcon(savedTheme);

  themeToggle.addEventListener('click', function() {
    const currentTheme = document.body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
  });

  function updateThemeIcon(theme) {
    themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
  }

  // Active nav link highlighting
  const currentPath = window.location.pathname;
  const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

  navLinks.forEach(link => {
    if (link.getAttribute('href') === currentPath) {
      link.classList.add('active');
    }
  });

  // Weather widget (mock data)
  function updateWeather() {
    const weatherWidget = document.querySelector('.weather-widget');
    if (weatherWidget) {
      // Mock weather data - in real app, fetch from weather API
      const temps = [18, 22, 25, 28, 24, 20];
      const icons = ['fa-sun', 'fa-cloud-sun', 'fa-cloud', 'fa-cloud-rain'];

      const temp = temps[Math.floor(Math.random() * temps.length)];
      const icon = icons[Math.floor(Math.random() * icons.length)];

      weatherWidget.querySelector('.weather-temp').textContent = `${temp}°C`;
      weatherWidget.querySelector('.weather-icon').className = `fas ${icon} weather-icon`;
    }
  }

  // Update weather every 5 minutes
  updateWeather();
  setInterval(updateWeather, 300000);

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Add loading state to buttons
  document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function() {
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
      }
    });
  });
});

// Add CSS for weather widget
const weatherStyles = `
  .weather-widget {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .weather-temp {
    font-weight: 600;
  }

  .weather-icon {
    color: var(--primary-green);
  }
`;

// Inject weather styles
const styleSheet = document.createElement('style');
styleSheet.textContent = weatherStyles;
document.head.appendChild(styleSheet);
</script>
