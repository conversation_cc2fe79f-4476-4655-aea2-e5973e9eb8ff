
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Poppins:wght@400;500;600&display=swap');

:root {
    --primary-bg: #e8f5e9;
    --secondary-bg: #c8e6c9;
    --card-bg: #f1f8e9;
    --text-main: #388e3c;
    --nav-bg: linear-gradient(90deg, #388e3c 0%, #81c784 100%);
    --profile-bold: 700;
    --profile-size: 1.3rem;
}

body[data-theme="dark"] {
    --primary-bg: #181a1b;
    --secondary-bg: #232526;
    --card-bg: #222326;
    --text-main: #b9cbb0;
    --nav-bg: linear-gradient(90deg, #181a1b 0%, #232526 100%);
}

body {
    background: var(--primary-bg);
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
    min-height: 100vh;
    background-image:
        linear-gradient(rgba(24,26,27,0.92), rgba(35,37,38,0.92)),
        url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1500&q=80');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.5s, color 0.5s;
    color: var(--text-main);
}
body[data-theme="dark"] {
    background-image:
        linear-gradient(rgba(24,26,27,0.97), rgba(35,37,38,0.97)),
        url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1500&q=80');
}

.navbar {
    margin-bottom: 2rem;
    background: var(--nav-bg);
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    border-radius: 0 0 16px 16px;
}

.card {
    box-shadow: 0 4px 16px rgba(0,0,0,0.22);
    border-radius: 16px;
    border: none;
    background: var(--card-bg);
    transition: box-shadow 0.3s, transform 0.3s;
}
.card:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.32);
    transform: translateY(-2px) scale(1.01);
}

.btn-primary, .btn-success {
    background-color: #388e3c;
    border-color: #388e3c;
    transition: background 0.3s, box-shadow 0.3s;
    box-shadow: 0 2px 8px rgba(56, 142, 60, 0.08);
}
.btn-primary:hover, .btn-success:hover {
    background-color: #2e7031;
    border-color: #2e7031;
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.16);
}

.container h1, .container h2, .container h3, .container h4 {
    color: var(--text-main);
    font-family: 'Merriweather', serif;
    letter-spacing: 2px;
    text-shadow: 0 2px 8px #c8e6c9;
    transition: color 0.3s, text-shadow 0.3s;
}

.profile-avatar {
    border: 4px solid #388e3c;
    box-shadow: 0 2px 8px rgba(56, 142, 60, 0.15);
    transition: transform 0.2s;
}
.profile-avatar:hover {
    transform: scale(1.03);
}

.profile-name {
    font-weight: var(--profile-bold);
    font-size: var(--profile-size);
    color: var(--text-main);
    letter-spacing: 1px;
}

.badge-success, .badge-info {
    font-size: 2rem;
    padding: 0.5em 1em;
    border-radius: 12px;
    background: linear-gradient(90deg, #388e3c 0%, #81c784 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(56, 142, 60, 0.08);
    transition: box-shadow 0.2s;
}
.badge-success:hover, .badge-info:hover {
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.16);
}

.list-group-item {
    background: var(--card-bg);
    border: none;
    font-size: 1.05rem;
    transition: background 0.2s;
}
.list-group-item:hover {
    background: var(--secondary-bg);
}

footer {
    background: var(--nav-bg);
    color: #fff;
    padding: 1rem 0;
    text-align: center;
    margin-top: 2rem;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -2px 8px rgba(56, 142, 60, 0.08);
}



body {
    background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
    font-family: "Poppins", serif; 
    min-height: 100vh;
    background-image:
        linear-gradient(rgba(232,245,233,0.85), rgba(200,230,201,0.85)),
        url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1500&q=80'); /* Unsplash nature/agriculture */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 1s;
}

.navbar {
    margin-bottom: 2rem;
    background: linear-gradient(90deg, #388e3c 0%, #81c784 100%);
    box-shadow: 0 2px 8px rgba(56, 142, 60, 0.1);
    border-radius: 0 0 16px 16px;
}

.card {
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.12);
    border-radius: 16px;
    border: none;
    background: linear-gradient(120deg, #f1f8e9 0%, #dcedc8 100%);
    transition: box-shadow 0.3s, transform 0.3s;
}
.card:hover {
    box-shadow: 0 8px 24px rgba(56, 142, 60, 0.18);
    transform: translateY(-2px) scale(1.01);
}

.btn-primary, .btn-success {
    background-color: #388e3c;
    border-color: #388e3c;
    transition: background 0.3s, box-shadow 0.3s;
    box-shadow: 0 2px 8px rgba(56, 142, 60, 0.08);
}
.btn-primary:hover, .btn-success:hover {
    background-color: #2e7031;
    border-color: #2e7031;
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.16);
}

.container h1, .container h2, .container h3, .container h4 {
    color: #388e3c;
    font-family: 'Merriweather', serif;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px #c8e6c9;
    transition: color 0.3s, text-shadow 0.3s;
}

.profile-avatar {
    border: 4px solid #388e3c;
    box-shadow: 0 2px 8px rgba(56, 142, 60, 0.15);
    transition: transform 0.2s;
}
.profile-avatar:hover {
    transform: scale(1.03);
}

.badge-success, .badge-info {
    font-size: 1rem;
    padding: 0.5em 1em;
    border-radius: 12px;
    background: linear-gradient(90deg, #388e3c 0%, #81c784 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(56, 142, 60, 0.08);
    transition: box-shadow 0.2s;
}
.badge-success:hover, .badge-info:hover {
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.16);
}

.list-group-item {
    background: #f1f8e9;
    border: none;
    font-size: 1.05rem;
    transition: background 0.2s;
}
.list-group-item:hover {
    background: #dcedc8;
}

footer {
    background: linear-gradient(90deg, #388e3c 0%, #81c784 100%);
    color: #fff;
    padding: 1rem 0;
    text-align: center;
    margin-top: 2rem;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -2px 8px rgba(56, 142, 60, 0.08);
}
.send-btn{
    background:transparent;
    color: gray;
    text-allign: center;
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

