/* AgriGenius - Modern Agricultural Theme */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* CSS Variables for Theme Management */
:root {
    /* Light Theme Colors */
    --primary-green: #2e7d32;
    --secondary-green: #4caf50;
    --accent-green: #81c784;
    --light-green: #c8e6c9;
    --very-light-green: #e8f5e9;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fdf8;
    --bg-tertiary: #f1f8e9;
    --bg-card: #ffffff;

    /* Text Colors */
    --text-primary: #1b5e20;
    --text-secondary: #2e7d32;
    --text-muted: #6b7280;
    --text-light: #9ca3af;

    /* Border & Shadow */
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
    --gradient-secondary: linear-gradient(135deg, #81c784 0%, #a5d6a7 100%);
    --gradient-hero: linear-gradient(135deg, rgba(46, 125, 50, 0.9) 0%, rgba(76, 175, 80, 0.8) 100%);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
}

/* Dark Theme */
body[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;

    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-light: #64748b;

    --border-color: #334155;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    transition: all 0.3s ease;
    overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-green);
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-green);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-green);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--secondary-green);
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-secondary {
    background: var(--gradient-secondary);
}

.shadow-soft {
    box-shadow: var(--shadow-lg);
}

.border-radius-lg {
    border-radius: var(--radius-lg);
}

.border-radius-xl {
    border-radius: var(--radius-xl);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

.scale-hover {
    transition: transform 0.3s ease;
}

.scale-hover:hover {
    transform: scale(1.05);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Navigation Styles */
.navbar {
    background: var(--bg-card) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-md) 0;
    transition: all 0.3s ease;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-family: var(--font-heading);
    font-weight: 700;
    font-size: var(--font-size-2xl);
    color: var(--primary-green) !important;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.navbar-brand i {
    font-size: var(--font-size-3xl);
    color: var(--secondary-green);
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-secondary) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-green) !important;
    background-color: var(--very-light-green);
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    color: var(--primary-green) !important;
    background-color: var(--light-green);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

/* Theme Toggle Button */
.theme-toggle {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    transition: all 0.3s ease;
    font-size: var(--font-size-lg);
}

.theme-toggle:hover {
    background: var(--primary-green);
    color: white;
    transform: rotate(180deg);
}

/* User Dropdown */
.dropdown-menu {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.dropdown-item {
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dropdown-item:hover {
    background: var(--very-light-green);
    color: var(--primary-green);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* User Avatar */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid var(--primary-green);
    object-fit: cover;
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.1);
    border-color: var(--secondary-green);
}

/* Card Styles */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
    border-color: var(--accent-green);
}

.card-header {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

.card-title {
    font-family: var(--font-heading);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Special Card Variants */
.card-gradient {
    background: var(--gradient-secondary);
    color: white;
}

.card-gradient .card-title,
.card-gradient .card-text {
    color: white;
}

.card-feature {
    text-align: center;
    padding: var(--spacing-2xl);
}

.card-feature .feature-icon {
    font-size: 3rem;
    color: var(--primary-green);
    margin-bottom: var(--spacing-lg);
    display: block;
}

.card-stat {
    background: var(--gradient-primary);
    color: white;
    text-align: center;
    padding: var(--spacing-xl);
}

.card-stat .stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    display: block;
    margin-bottom: var(--spacing-sm);
}

.card-stat .stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Button Styles */
.btn {
    font-weight: 500;
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    font-family: var(--font-primary);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--accent-green);
    color: white;
    border-color: var(--accent-green);
}

.btn-success {
    background: var(--secondary-green);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: var(--primary-green);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-outline-primary:hover {
    background: var(--primary-green);
    color: white;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    border-radius: var(--radius-xl);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.btn-floating {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    border: none;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: all 0.3s ease;
}

.btn-floating:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

/* Form Styles */
.form-control {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.form-text {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group .form-control {
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.input-group-text {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-left: none;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    padding: var(--spacing-md);
    color: var(--text-secondary);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

.alert-success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--primary-green);
    border-left: 4px solid var(--secondary-green);
}

.alert-danger {
    background: rgba(244, 67, 54, 0.1);
    color: #d32f2f;
    border-left: 4px solid #f44336;
}

.alert-info {
    background: rgba(33, 150, 243, 0.1);
    color: #1976d2;
    border-left: 4px solid #2196f3;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #f57c00;
    border-left: 4px solid #ffc107;
}

/* Badge Styles */
.badge {
    font-size: var(--font-size-xs);
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: var(--secondary-green);
    color: white;
}

.badge-warning {
    background: #ffc107;
    color: #212529;
}

.badge-info {
    background: #2196f3;
    color: white;
}

.badge-danger {
    background: #f44336;
    color: white;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-hero);
    color: white;
    padding: var(--spacing-2xl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?auto=format&fit=crop&w=1500&q=80') center/cover;
    opacity: 0.2;
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    opacity: 0.9;
}

/* Feature Grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.feature-card {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-green);
    margin-bottom: var(--spacing-lg);
    display: block;
}

/* Stats Section */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-2xl) 0;
}

.stat-card {
    background: var(--gradient-primary);
    color: white;
    text-align: center;
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    display: block;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* List Group */
.list-group-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
    border-radius: var(--radius-lg) !important;
    margin-bottom: var(--spacing-sm);
}

.list-group-item:hover {
    background: var(--very-light-green);
    border-color: var(--accent-green);
    transform: translateX(4px);
}

.list-group-item:first-child {
    border-top-left-radius: var(--radius-lg) !important;
    border-top-right-radius: var(--radius-lg) !important;
}

.list-group-item:last-child {
    border-bottom-left-radius: var(--radius-lg) !important;
    border-bottom-right-radius: var(--radius-lg) !important;
}

/* Footer */
footer {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-2xl) 0;
    margin-top: var(--spacing-2xl);
    text-align: center;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h5 {
    color: white;
    margin-bottom: var(--spacing-md);
}

.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: var(--spacing-lg);
    opacity: 0.8;
}


/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .navbar-nav {
        padding: var(--spacing-md) 0;
    }

    .container {
        padding: 0 var(--spacing-md);
    }

    .btn-floating {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .hero-section {
        padding: var(--spacing-xl) 0;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-green);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chatbot Specific Styles */
.chat-container {
    max-width: 800px;
    margin: 0 auto;
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.chat-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
    text-align: center;
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
}

.message {
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.message.user {
    flex-direction: row-reverse;
}

.message-content {
    background: var(--bg-card);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    max-width: 70%;
    box-shadow: var(--shadow-sm);
}

.message.user .message-content {
    background: var(--primary-green);
    color: white;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--accent-green);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    flex-shrink: 0;
}

.chat-input {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-card);
}

.input-with-button {
    display: flex;
    gap: var(--spacing-sm);
}

.input-with-button .form-control {
    flex: 1;
}

/* Marketplace Styles */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.product-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--bg-tertiary);
}

.product-info {
    padding: var(--spacing-lg);
}

.product-title {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.product-price {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: var(--spacing-md);
}

.product-description {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

