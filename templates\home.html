<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgriGenius - Smart Agriculture Platform</title>
    <meta name="description" content="Revolutionary agricultural platform combining AI, IoT sensors, and community knowledge to transform modern farming.">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" as="style">
</head>
<body class="fade-in">
    {% include 'navbar.html' %}

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content slide-up">
                <h1 class="hero-title">
                    Welcome to <span class="text-gradient">AgriGenius</span>
                </h1>
                <p class="hero-subtitle">
                    Revolutionizing agriculture through AI-powered insights, real-time monitoring, and community-driven knowledge sharing.
                </p>
                <div class="hero-actions">
                    <a href="/chat" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-robot"></i> Try AI Assistant
                    </a>
                    <a href="/articles" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-newspaper"></i> Explore Articles
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Live Stats Section -->
    <section class="py-5">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-card scale-hover">
                    <span class="stat-number" id="userCount">1,247</span>
                    <span class="stat-label">Active Farmers</span>
                </div>
                <div class="stat-card scale-hover">
                    <span class="stat-number" id="articleCount">856</span>
                    <span class="stat-label">Knowledge Articles</span>
                </div>
                <div class="stat-card scale-hover">
                    <span class="stat-number" id="sensorCount">2,341</span>
                    <span class="stat-label">Sensor Readings</span>
                </div>
                <div class="stat-card scale-hover">
                    <span class="stat-number" id="successRate">94%</span>
                    <span class="stat-label">Success Rate</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-gradient">Powerful Features for Modern Farming</h2>
                <p class="lead text-muted">Everything you need to optimize your agricultural operations</p>
            </div>

            <div class="feature-grid">
                <div class="feature-card scale-hover">
                    <i class="feature-icon fas fa-brain"></i>
                    <h4>AI-Powered Insights</h4>
                    <p>Get intelligent recommendations based on real-time data analysis and machine learning algorithms.</p>
                    <a href="/chat" class="btn btn-outline-primary">Try Now</a>
                </div>

                <div class="feature-card scale-hover">
                    <i class="feature-icon fas fa-satellite-dish"></i>
                    <h4>IoT Sensor Network</h4>
                    <p>Monitor soil moisture, temperature, pH levels, and nutrients with our advanced sensor technology.</p>
                    <a href="/dashboard" class="btn btn-outline-primary">View Dashboard</a>
                </div>

                <div class="feature-card scale-hover">
                    <i class="feature-icon fas fa-users"></i>
                    <h4>Community Knowledge</h4>
                    <p>Share experiences, learn from experts, and collaborate with farmers worldwide.</p>
                    <a href="/articles" class="btn btn-outline-primary">Join Community</a>
                </div>

                <div class="feature-card scale-hover">
                    <i class="feature-icon fas fa-store"></i>
                    <h4>Marketplace</h4>
                    <p>Buy and sell agricultural products, tools, and equipment in our integrated marketplace.</p>
                    <a href="/marketplace" class="btn btn-outline-primary">Browse Products</a>
                </div>

                <div class="feature-card scale-hover">
                    <i class="feature-icon fas fa-chart-line"></i>
                    <h4>Analytics & Reports</h4>
                    <p>Track your farm's performance with detailed analytics and customizable reports.</p>
                    <a href="/dashboard" class="btn btn-outline-primary">View Analytics</a>
                </div>

                <div class="feature-card scale-hover">
                    <i class="feature-icon fas fa-mobile-alt"></i>
                    <h4>Mobile Ready</h4>
                    <p>Access all features on-the-go with our responsive design and mobile-optimized interface.</p>
                    <a href="#" class="btn btn-outline-primary">Download App</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Real-time Sensor Data Section -->
    <section class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-6 fw-bold mb-4">Real-time Farm Monitoring</h2>
                    <p class="lead mb-4">Stay connected to your farm 24/7 with our advanced IoT sensor network. Monitor critical parameters and receive instant alerts.</p>
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-thermometer-half text-danger fs-2 mb-2"></i>
                                    <h5 class="card-title">Temperature</h5>
                                    <p class="card-text fs-4 fw-bold" id="liveTemp">24°C</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-tint text-primary fs-2 mb-2"></i>
                                    <h5 class="card-title">Humidity</h5>
                                    <p class="card-text fs-4 fw-bold" id="liveHumidity">68%</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-seedling text-success fs-2 mb-2"></i>
                                    <h5 class="card-title">Soil Moisture</h5>
                                    <p class="card-text fs-4 fw-bold" id="liveSoil">45%</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-flask text-warning fs-2 mb-2"></i>
                                    <h5 class="card-title">pH Level</h5>
                                    <p class="card-text fs-4 fw-bold" id="livePH">6.8</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card shadow-lg">
                        <div class="card-header bg-gradient-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-area"></i> Live Sensor Chart</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="sensorChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-5 bg-gradient-primary text-white">
        <div class="container text-center">
            <h2 class="display-5 fw-bold mb-4">Ready to Transform Your Farm?</h2>
            <p class="lead mb-4">Join thousands of farmers who are already using AgriGenius to optimize their operations.</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    {% if not current_user.is_authenticated %}
                    <a href="/signup" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-user-plus"></i> Get Started Free
                    </a>
                    <a href="/chat" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-robot"></i> Try AI Assistant
                    </a>
                    {% else %}
                    <a href="/dashboard" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-chart-line"></i> View Dashboard
                    </a>
                    <a href="/chat" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-robot"></i> Ask AI Assistant
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Animated counters for stats
        function animateCounter(element, target, duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (element.id === 'successRate') {
                    element.textContent = Math.floor(current) + '%';
                } else {
                    element.textContent = Math.floor(current).toLocaleString();
                }
            }, 16);
        }

        // Live sensor data simulation
        function updateSensorData() {
            const temp = Math.floor(Math.random() * 10) + 20;
            const humidity = Math.floor(Math.random() * 20) + 60;
            const soil = Math.floor(Math.random() * 30) + 35;
            const ph = (Math.random() * 2 + 6).toFixed(1);

            document.getElementById('liveTemp').textContent = temp + '°C';
            document.getElementById('liveHumidity').textContent = humidity + '%';
            document.getElementById('liveSoil').textContent = soil + '%';
            document.getElementById('livePH').textContent = ph;
        }

        // Initialize chart
        function initSensorChart() {
            const ctx = document.getElementById('sensorChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['6h ago', '5h ago', '4h ago', '3h ago', '2h ago', '1h ago', 'Now'],
                    datasets: [{
                        label: 'Temperature (°C)',
                        data: [22, 24, 23, 25, 26, 24, 23],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Humidity (%)',
                        data: [65, 68, 70, 67, 64, 66, 68],
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Animate counters
            animateCounter(document.getElementById('userCount'), 1247);
            animateCounter(document.getElementById('articleCount'), 856);
            animateCounter(document.getElementById('sensorCount'), 2341);
            animateCounter(document.getElementById('successRate'), 94);

            // Initialize chart
            initSensorChart();

            // Update sensor data every 5 seconds
            updateSensorData();
            setInterval(updateSensorData, 5000);

            // Add scroll animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('slide-up');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.feature-card, .stat-card').forEach(el => {
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
